import { z } from "zod";
import { revisionSchema, instructionSchema, insertSchema, deleteSchema, replaceSchema } from "@/lib/schemas";

// Infer types from schemas
export type Instruction = z.infer<typeof instructionSchema>;
export type InsertInstruction = z.infer<typeof insertSchema>;
export type DeleteInstruction = z.infer<typeof deleteSchema>;
export type ReplaceInstruction = z.infer<typeof replaceSchema>;
export type Revision = z.infer<typeof revisionSchema>;

// Union type for all instruction types
export type InstructionType = InsertInstruction | DeleteInstruction | ReplaceInstruction;
