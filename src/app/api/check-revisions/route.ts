import { NextResponse } from "next/server";
import OpenAI from "openai";
import { zodTextFormat } from "openai/helpers/zod";
import { GoogleGenAI, Type } from "@google/genai";
import fs from "fs/promises";
import path from "path";
import { revisionCheckSchema } from "@/lib/schemas";
import type { RevisionCheck, Revision } from "@/types/revision";

const gemini = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const model = formData.get("model") as string;
    const imageBefore = formData.get("imageBefore") as string;
    const imageAfter = formData.get("imageAfter") as string;
    const revisionsJson = formData.get("revisions") as string;

    // Validate required inputs
    if (!imageBefore || !imageAfter || !revisionsJson) {
      return NextResponse.json(
        { error: "imageBefore, imageAfter, and revisions are required" },
        { status: 400 }
      );
    }

    let revisions: Revision;
    try {
      revisions = JSON.parse(revisionsJson);
    } catch (error) {
      return NextResponse.json(
        { error: "Invalid revisions JSON format" },
        { status: 400 }
      );
    }

    // Extract base64 data from data URLs
    const base64Before = imageBefore.replace(/^data:image\/[a-z]+;base64,/, "");
    const base64After = imageAfter.replace(/^data:image\/[a-z]+;base64,/, "");
    const imageBeforeBuffer = Buffer.from(base64Before, "base64").buffer;
    const imageAfterBuffer = Buffer.from(base64After, "base64").buffer;

    const instruction =
      "優秀な画像認識AIとして、修正前後の画像と修正指示を比較し、各修正指示が正しく適用されているかを確認してください。";
    const prompt = await fs.readFile(
      path.join(process.cwd(), "src/app/api/check-revisions/prompt.md"),
      "utf-8"
    );

    // Create detailed prompt with revision instructions
    const detailedPrompt = `${prompt}

## 確認対象の修正指示

${revisions.instructions
  .map(
    (instruction, index) => `
### 修正指示 ${index + 1}
- タイプ: ${instruction.type}
- 位置: ${instruction.position}
${
  instruction.type === "insert"
    ? `- 挿入テキスト: ${(instruction as any).text}`
    : ""
}
${
  instruction.type === "delete"
    ? `- 削除テキスト: ${(instruction as any).text}`
    : ""
}
${
  instruction.type === "replace"
    ? `- 置換前: ${(instruction as any).before}\n- 置換後: ${
        (instruction as any).after
      }`
    : ""
}
`
  )
  .join("")}

上記の修正指示が修正前画像から修正後画像への変更で正しく適用されているかを確認してください。`;

    let checkResult: RevisionCheck;

    switch (model.split("-")[0]) {
      case "gemini": {
        const response = await gemini.models.generateContent({
          model: model,
          contents: [
            {
              inlineData: {
                mimeType: "image/jpeg" as const,
                data: Buffer.from(imageBeforeBuffer).toString("base64"),
              },
            },
            {
              inlineData: {
                mimeType: "image/jpeg" as const,
                data: Buffer.from(imageAfterBuffer).toString("base64"),
              },
            },
            { text: detailedPrompt },
          ],
          config: {
            temperature: 0.2,
            systemInstruction: instruction,
            responseMimeType: "application/json",
            responseSchema: {
              type: Type.OBJECT,
              properties: {
                results: {
                  type: Type.ARRAY,
                  items: {
                    type: Type.OBJECT,
                    properties: {
                      instructionIndex: {
                        type: Type.NUMBER,
                        description: "Index of the instruction (0-based)",
                        nullable: false,
                      },
                      isCorrectlyApplied: {
                        type: Type.BOOLEAN,
                        description:
                          "Whether the instruction was correctly applied",
                        nullable: false,
                      },
                      explanation: {
                        type: Type.STRING,
                        description: "Detailed explanation of the assessment",
                        nullable: false,
                      },
                      confidence: {
                        type: Type.NUMBER,
                        description: "Confidence level (0.0-1.0)",
                        nullable: false,
                      },
                    },
                    required: [
                      "instructionIndex",
                      "isCorrectlyApplied",
                      "explanation",
                      "confidence",
                    ],
                    propertyOrdering: [
                      "instructionIndex",
                      "isCorrectlyApplied",
                      "explanation",
                      "confidence",
                    ],
                  },
                },
                overallAccuracy: {
                  type: Type.NUMBER,
                  description: "Overall accuracy (0.0-1.0)",
                  nullable: false,
                },
                summary: {
                  type: Type.STRING,
                  description: "Summary of the revision check",
                  nullable: false,
                },
              },
              required: ["results", "overallAccuracy", "summary"],
              propertyOrdering: ["results", "overallAccuracy", "summary"],
            },
          },
        });
        checkResult = JSON.parse(response.text ?? "{}") as RevisionCheck;
        break;
      }

      default: {
        // OpenAI
        const response = await openai.responses.parse({
          model: model,
          temperature: model.startsWith("gpt") ? 0.2 : null,
          instructions: instruction,
          input: [
            {
              role: "user",
              content: [
                { type: "input_text", text: detailedPrompt },
                {
                  type: "input_image" as const,
                  image_url: `data:image/jpeg;base64,${Buffer.from(
                    imageBeforeBuffer
                  ).toString("base64")}`,
                  detail: "auto" as const,
                },
                {
                  type: "input_image" as const,
                  image_url: `data:image/jpeg;base64,${Buffer.from(
                    imageAfterBuffer
                  ).toString("base64")}`,
                  detail: "auto" as const,
                },
              ],
            },
          ],
          text: {
            format: zodTextFormat(revisionCheckSchema, "checkResult"),
          },
        });
        checkResult = response.output_parsed ?? ({} as RevisionCheck);
        break;
      }
    }

    return NextResponse.json({ checkResult });
  } catch (error) {
    console.error("Error processing files:", error);
    return NextResponse.json(
      { error: "Failed to process files" },
      { status: 500 }
    );
  }
}

export const config = {
  api: {
    bodyParser: false,
  },
};
