# 修正指示適用確認タスク

## 目的

修正前の画像、修正後の画像、および修正指示のリストを比較し、各修正指示が正しく適用されているかを確認する。

## 入力情報

- **修正前画像**: 元の文書画像
- **修正後画像**: 修正が適用された後の文書画像
- **修正指示リスト**: 適用されるべき修正指示の配列
  - **挿入(insert)**: 指定位置に新しいテキストを挿入
  - **削除(delete)**: 指定されたテキストを削除
  - **置換(replace)**: 指定されたテキストを別のテキストに置き換え

## 処理手順

1. 修正前画像と修正後画像のテキストを認識・比較
2. 各修正指示について以下を確認：
   - 指示された位置で修正が行われているか
   - 修正内容が指示通りに実行されているか
   - 修正により意図しない変更が発生していないか
3. 各修正指示の適用状況を評価
4. 全体的な修正精度を算出

## 出力形式

各修正指示について以下の情報を含む JSON を出力：

- `instructionIndex`: 修正指示のインデックス（0 から開始）
- `isCorrectlyApplied`: 修正が正しく適用されているか（boolean）
- `explanation`: 判定理由の詳細説明
- `confidence`: 判定の信頼度（0.0-1.0）

全体の結果として：

- `overallAccuracy`: 全体的な修正精度（0.0-1.0）
- `summary`: 修正結果の総合評価

## 注意事項

- 文字認識の精度を考慮し、軽微な誤字脱字は許容する
- 修正指示の意図を理解し、形式的な一致だけでなく内容的な正確性も評価する
- 修正により文章の意味や文脈が変わっていないかも確認する
- 判定に迷う場合は、その理由を explanation に詳しく記載する
