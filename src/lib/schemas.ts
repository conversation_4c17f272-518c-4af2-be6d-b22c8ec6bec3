import { z } from "zod";

const instructionSchema = z.object({
  type: z.enum(["insert", "delete", "replace"]),
  position: z.string(),
  applied: z.boolean(),
});

const insertSchema = instructionSchema.extend({
  type: z.literal("insert"),
  text: z.string(),
});

const deleteSchema = instructionSchema.extend({
  type: z.literal("delete"),
  text: z.string(),
});

const replaceSchema = instructionSchema.extend({
  type: z.literal("replace"),
  before: z.string(),
  after: z.string(),
});

export const revisionSchema = z.object({
  instructions: z.array(insertSchema.or(deleteSchema).or(replaceSchema)),
});

// Schema for revision check result
const revisionCheckResultSchema = z.object({
  instructionIndex: z.number(),
  isCorrectlyApplied: z.boolean(),
  explanation: z.string(),
  confidence: z.number().min(0).max(1),
});

export const revisionCheckSchema = z.object({
  results: z.array(revisionCheckResultSchema),
  overallAccuracy: z.number().min(0).max(1),
  summary: z.string(),
});

// Export individual schemas if needed elsewhere
export {
  instructionSchema,
  insertSchema,
  deleteSchema,
  replaceSchema,
  revisionCheckResultSchema,
};
